{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "build:fallback": "nuxt generate", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "server": "echo 'Build completed successfully'", "test:supabase": "cd scripts && tsx --tsconfig tsconfig.json test-supabase-connection.ts", "migrate:supabase": "cd scripts && tsx --tsconfig tsconfig.json migrate-to-supabase.ts", "update:dao": "cd scripts && tsx --tsconfig tsconfig.json update-dao-imports.ts", "deploy:cloudflare": "npm run build && npx wrangler pages deploy .output/public --project-name=grap"}, "dependencies": {"@nuxt/image": "1.10.0", "@supabase/supabase-js": "^2.49.8", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "nuxt": "^3.17.3", "proj4": "^2.17.0", "sanitize-html": "^2.17.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "xml2js": "^0.6.2"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.14.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/proj4": "^2.5.6", "@types/sanitize-html": "^2", "@types/xml2js": "^0.4.14", "dotenv": "^16.5.0", "tsx": "^4.19.4", "wrangler": "^4.16.1"}, "packageManager": "yarn@4.9.1"}